<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Quiz Page - Auto Quiz Demo</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .quiz-container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .question {
            margin-bottom: 20px;
        }
        .question h3 {
            color: #333;
            margin-bottom: 10px;
        }
        .answer-choice {
            display: block;
            margin: 8px 0;
            padding: 10px;
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            cursor: pointer;
            transition: all 0.2s;
        }
        .answer-choice:hover {
            background: #e9ecef;
        }
        .answer-choice.selected {
            background: #d4edda;
            border-color: #c3e6cb;
        }
        input[type="radio"], input[type="checkbox"] {
            margin-right: 8px;
        }
        .quiz-option {
            display: inline-block;
            margin: 5px 10px 5px 0;
            padding: 8px 15px;
            background: #007bff;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
        }
        .quiz-option:hover {
            background: #0056b3;
        }
        .quiz-option.selected {
            background: #28a745;
        }
        .status {
            position: fixed;
            top: 10px;
            right: 10px;
            padding: 10px;
            background: #17a2b8;
            color: white;
            border-radius: 4px;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <div class="status">Auto Quiz Test Page - Enable Auto Quiz in settings to test</div>
    
    <h1>Sample Quiz Questions</h1>
    <p>This page simulates quiz questions to test the Auto Quiz feature. Open the settings panel and enable "Auto Quiz" to see it in action.</p>

    <div class="quiz-container">
        <div class="question">
            <h3>Question 1: What is 2 + 2?</h3>
            <label class="answer-choice">
                <input type="radio" name="q1" id="answer1a" value="3"> 3
            </label>
            <label class="answer-choice">
                <input type="radio" name="q1" id="answer1b" value="4"> 4 (Correct)
            </label>
            <label class="answer-choice">
                <input type="radio" name="q1" id="answer1c" value="5"> 5
            </label>
        </div>
    </div>

    <div class="quiz-container">
        <div class="question">
            <h3>Question 2: Which of these are programming languages? (Multiple choice)</h3>
            <label class="answer-choice">
                <input type="checkbox" id="answer2a" value="javascript"> JavaScript (Correct)
            </label>
            <label class="answer-choice">
                <input type="checkbox" id="answer2b" value="python"> Python (Correct)
            </label>
            <label class="answer-choice">
                <input type="checkbox" id="answer2c" value="html"> HTML
            </label>
        </div>
    </div>

    <div class="quiz-container">
        <div class="question">
            <h3>Question 3: Click the correct answer</h3>
            <button class="quiz-option" data-answer="wrong1">Wrong Answer 1</button>
            <button class="quiz-option" data-answer="correct">Correct Answer</button>
            <button class="quiz-option" data-answer="wrong2">Wrong Answer 2</button>
        </div>
    </div>

    <div class="quiz-container">
        <div class="question">
            <h3>Question 4: Select the right option</h3>
            <div class="answer-choice" id="option4a">Option A</div>
            <div class="answer-choice" id="option4b">Option B (Correct)</div>
            <div class="answer-choice" id="option4c">Option C</div>
        </div>
    </div>

    <script>
        // Add click handlers to simulate quiz behavior
        document.querySelectorAll('.answer-choice').forEach(choice => {
            choice.addEventListener('click', function() {
                // For radio buttons, clear other selections in the same group
                if (this.querySelector('input[type="radio"]')) {
                    const name = this.querySelector('input').name;
                    document.querySelectorAll(`input[name="${name}"]`).forEach(radio => {
                        radio.closest('.answer-choice').classList.remove('selected');
                    });
                }
                
                this.classList.toggle('selected');
                const input = this.querySelector('input');
                if (input) {
                    if (input.type === 'radio') {
                        input.checked = true;
                    } else if (input.type === 'checkbox') {
                        input.checked = !input.checked;
                    }
                }
            });
        });

        document.querySelectorAll('.quiz-option').forEach(option => {
            option.addEventListener('click', function() {
                // Clear other selections in the same question
                this.parentElement.querySelectorAll('.quiz-option').forEach(opt => {
                    opt.classList.remove('selected');
                });
                this.classList.add('selected');
            });
        });

        // Log when elements are clicked for testing
        document.addEventListener('click', function(e) {
            if (e.target.hasAttribute('data-auto-clicked')) {
                console.log('Auto Quiz clicked:', e.target);
            }
        });
    </script>
</body>
</html>
